apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dev-frontend-ingress
  namespace: logi-dev
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - dev.logiancelogistics.com
    secretName: dev-logi-tls
  rules:
  - host: dev.logiancelogistics.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: web-frontend-dev
            port:
              number: 80  # Must match Service port, not targetPort