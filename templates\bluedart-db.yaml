apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: bluedart-db-dev
  name: bluedart-db-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bluedart-db-dev
  serviceName: bluedart-db-dev
  template:
    metadata:
      labels:
        app: bluedart-db-dev
    spec:
      containers:
      - env:
        - name: POSTGRES_PASSWORD
          value: bluedart_password_dev
        - name: POSTGRES_USER
          value: bluedart_user_dev
        - name: POSTGRES_DB
          value: bluedart_db_dev
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        image: logicontainer.azurecr.io/logiance-logistics-dev:bluedart_db_{{Build.BuildId}}
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U bluedart_user_dev -d bluedart_db_dev
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        name: bluedart-db-dev
        ports:
        - containerPort: 5432
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U bluedart_user_dev -d bluedart_db_dev
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 500m
            memory: 2048Mi
          requests:
            cpu: 100m
            memory: 500Mi
        volumeMounts:
        - mountPath: /var/lib/postgresql/data
          name: bluedart-postgres-data
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
      volumes:
      - name: bluedart-postgres-data
        persistentVolumeClaim:
          claimName: bluedart-postgres-data-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: bluedart-db-dev
  name: bluedart-db-dev
spec:
  clusterIP: None
  ports:
  - name: tcp
    port: '{{ .Values.service.port }}'
    targetPort: 5432
  selector:
    app: bluedart-db-dev
  type: '{{ .Values.service.type }}'
---
allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: azure-disk-sc-bluedart-db-dev
parameters:
  skuName: StandardSSD_LRS
provisioner: disk.csi.azure.com
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bluedart-postgres-data-dev
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 30Gi
  storageClassName: azure-disk-sc-bluedart-db-dev
