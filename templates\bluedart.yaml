apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: bluedart-service-dev
  name: bluedart-service-dev
spec:
  replicas: '{{ .Values.replicaCount }}'
  selector:
    matchLabels:
      app: bluedart-service-dev
  template:
    metadata:
      labels:
        app: bluedart-service-dev
    spec:
      containers:
      - env:
        - name: SERVER_PORT
          value: '8080'
        - name: GRPC_PORT
          value: '8081'
        - name: ENVIRONMENT
          value: development
        - name: LOG_LEVEL
          value: debug
        - name: BLUEDART_CLIENT_ID
          value: UdQeMUR7KX0P9yTywaZAEgf6hAucUm49
        - name: BLUEDART_CLIENT_SECRET
          value: vI7AfX1mLwACY74A
        - name: BLUEDART_LOGIN_ID
          value: GG940111
        - name: BLUEDART_LICENSE_KEY
          value: kh7mnhqkmgegoksipxr0urmqesesseup
        - name: BLUEDART_PRODUCTION
          value: 'false'
        - name: BLUEDART_STAGING_URL
          value: https://apigateway-sandbox.bluedart.com
        - name: BLUEDART_PRODUCTION_URL
          value: https://apigateway.bluedart.com
        - name: BLUEDART_TOKEN_EXPIRY_MINUTES
          value: '1440'
        - name: BLUEDART_TIMEOUT
          value: '10'
        - name: DB_HOST
          value: bluedart-db-dev
        - name: DB_PORT
          value: '5432'
        - name: DB_NAME
          value: bluedart_db_dev
        - name: DB_USER
          value: bluedart_user_dev
        - name: DB_PASSWORD
          value: bluedart_password_dev
        - name: DB_SSL_MODE
          value: disabled
        - name: DB_MAX_CONNECTIONS
          value: '20'
        - name: DB_IDLE_CONNECTIONS
          value: '5'
        - name: AZURE_CONNECTION_STRING
          value: DefaultEndpointsProtocol=https;AccountName=labelstore;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
        - name: AZURE_CONTAINER_NAME
          value: bluedart
        image: '{{ .Values.image.repository }}:{{ .Values.image.tag }}'
        imagePullPolicy: '{{ .Values.image.pullPolicy }}'
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        name: bluedart-service-dev
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: grpc
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 400m
            memory: 1500Mi
          requests:
            cpu: 100m
            memory: 500Mi
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: bluedart-service-dev
  name: bluedart-service-dev
spec:
  ports:
  - name: grpc
    port: '{{ .Values.service.port }}'
    targetPort: 8081
  - name: http
    port: 8080
    targetPort: 8080
  selector:
    app: bluedart-service-dev
  type: '{{ .Values.service.type }}'
