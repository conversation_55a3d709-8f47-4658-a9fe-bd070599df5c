apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ndr-service-dev
  name: ndr-service-dev
spec:
  replicas: '{{ .Values.replicaCount }}'
  selector:
    matchLabels:
      app: ndr-service-dev
  template:
    metadata:
      labels:
        app: ndr-service-dev
    spec:
      containers:
      - env:
        - name: POSTGRES_HOST
          value: ndr-db-dev
        - name: POSTGRES_PORT
          value: '5432'
        - name: POSTGRES_USER
          value: ndr_user_dev
        - name: POSTGRES_PASSWORD
          value: ndr_password_dev
        - name: POSTGRES_DBNAME
          value: ndr_db_dev
        - name: POSTGRES_SSLMODE
          value: disable
        - name: REDIS_ADDRESS
          value: localhost:6379
        - name: REDIS_PASSWORD
          value: ''
        - name: RABBITMQ_HOST
          value: localhost
        - name: RABBITMQ_PORT
          value: '5672'
        - name: RABBITMQ_USER
          value: guest
        - name: RABB<PERSON>MQ_PASSWORD
          value: guest
        - name: RABBITMQ_VHOST
          value: /
        - name: RA<PERSON><PERSON><PERSON>Q_EXCHANGE_NAME
          value: ndr_events
        - name: RA<PERSON><PERSON>MQ_CREATED_QUEUE
          value: ndr.created
        - name: RABBITMQ_UPDATED_QUEUE
          value: ndr.updated
        - name: BLUEDART_GRPC_HOST
          value: bluedart-service-dev
        - name: BLUEDART_GRPC_PORT
          value: '8081'
        - name: DELHIVERY_GRPC_HOST
          value: localhost
        - name: DELHIVERY_GRPC_PORT
          value: '50051'
        - name: XPRESSBEES_GRPC_HOST
          value: xpressbees-service-dev
        - name: XPRESSBEES_GRPC_PORT
          value: '9000'
        - name: ENV
          value: production
        image: '{{ .Values.image.repository }}:{{ .Values.image.tag }}'
        imagePullPolicy: '{{ .Values.image.pullPolicy }}'
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        name: ndr-service-dev
        ports:
        - containerPort: 8080
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 400m
            memory: 1500Mi
          requests:
            cpu: 100m
            memory: 500Mi
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: ndr-service-dev
  name: ndr-service-dev
spec:
  ports:
  - name: grpc
    port: '{{ .Values.service.port }}'
    targetPort: 8080
  selector:
    app: ndr-service-dev
  type: '{{ .Values.service.type }}'
