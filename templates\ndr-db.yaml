apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: ndr-db-dev
  name: ndr-db-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ndr-db-dev
  serviceName: ndr-db-dev
  template:
    metadata:
      labels:
        app: ndr-db-dev
    spec:
      containers:
      - env:
        - name: POSTGRES_USER
          value: ndr_user_dev
        - name: POSTGRES_PASSWORD
          value: ndr_password_dev
        - name: POSTGRES_DB
          value: ndr_db_dev
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        image: logicontainer.azurecr.io/logiance-logistics-dev:ndr_db_{{Build.BuildId}}
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -h localhost -U ndr_user_dev -d ndr_db_dev
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        name: ndr-db-dev
        ports:
        - containerPort: 5432
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -h localhost -U ndr_user_dev -d ndr_db_dev
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 500m
            memory: 2048Mi
          requests:
            cpu: 100m
            memory: 500Mi
        volumeMounts:
        - mountPath: /var/lib/postgresql/data
          name: ndr-postgres-data
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
      volumes:
      - name: ndr-postgres-data
        persistentVolumeClaim:
          claimName: ndr-postgres-data-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: ndr-db-dev
  name: ndr-db-dev
spec:
  clusterIP: None
  ports:
  - name: tcp
    port: '{{ .Values.service.port }}'
    targetPort: 5432
  selector:
    app: ndr-db-dev
  type: '{{ .Values.service.type }}'
---
allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: azure-disk-sc-ndr-db-dev
parameters:
  skuName: StandardSSD_LRS
provisioner: disk.csi.azure.com
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ndr-postgres-data-dev
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 30Gi
  storageClassName: azure-disk-sc-ndr-db-dev
