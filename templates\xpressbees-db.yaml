apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: xpressbees-db-dev
  name: xpressbees-db-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: xpressbees-db-dev
  serviceName: xpressbees-db-dev
  template:
    metadata:
      labels:
        app: xpressbees-db-dev
    spec:
      containers:
      - env:
        - name: POSTGRES_USER
          value: xpressbees_user_dev
        - name: POSTGRES_PASSWORD
          value: xpressbees_password_dev
        - name: POSTGRES_DB
          value: xpressbees_db
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        image: logicontainer.azurecr.io/logiance-logistics-dev:xpressbees_db_{{Build.BuildId}}
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -h localhost -U xpressbees_user_dev -d xpressbees_db
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        name: xpressbees-db-dev
        ports:
        - containerPort: 5432
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -h localhost -U xpressbees_user_dev -d xpressbees_db
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 500m
            memory: 2048Mi
          requests:
            cpu: 100m
            memory: 500Mi
        volumeMounts:
        - mountPath: /var/lib/postgresql/data
          name: xpressbees-postgres-data
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
      volumes:
      - name: xpressbees-postgres-data
        persistentVolumeClaim:
          claimName: xpressbees-postgres-data-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: xpressbees-db-dev
  name: xpressbees-db-dev
spec:
  clusterIP: None
  ports:
  - name: tcp
    port: '{{ .Values.service.port }}'
    targetPort: 5432
  selector:
    app: xpressbees-db-dev
  type: '{{ .Values.service.type }}'
---
allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: azure-disk-sc-xpressbees-postgres
parameters:
  skuName: StandardSSD_LRS
provisioner: disk.csi.azure.com
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: xpressbees-postgres-data-dev
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 30Gi
  storageClassName: azure-disk-sc-xpressbees-postgres
