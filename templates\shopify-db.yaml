apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: shopify-db-dev
  name: shopify-db-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: shopify-db-dev
  serviceName: shopify-db-dev
  template:
    metadata:
      labels:
        app: shopify-db-dev
    spec:
      containers:
      - env:
        - name: POSTGRES_USER
          value: shopify_user_dev
        - name: POSTGRES_PASSWORD
          value: shopify_password_dev
        - name: POSTGRES_DB
          value: shopify_db_dev
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        image: logicontainer.azurecr.io/logiance-logistics-dev:shopify_db_{{Build.BuildId}}
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - shopify_user_dev
            - -d
            - shopify_db_dev
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        name: shopify-db-dev
        ports:
        - containerPort: 5432
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - shopify_user_dev
            - -d
            - shopify_db_dev
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 500m
            memory: 2048Mi
          requests:
            cpu: 100m
            memory: 500Mi
        volumeMounts:
        - mountPath: /var/lib/postgresql/data
          name: shopify-postgres-data
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
      volumes:
      - name: shopify-postgres-data
        persistentVolumeClaim:
          claimName: shopify-postgres-data-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: shopify-db-dev
  name: shopify-db-dev
spec:
  clusterIP: None
  ports:
  - name: tcp
    port: '{{ .Values.service.port }}'
    targetPort: 5432
  selector:
    app: shopify-db-dev
  type: '{{ .Values.service.type }}'
---
allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: azure-disk-sc-shopify-db-dev
parameters:
  skuName: StandardSSD_LRS
provisioner: disk.csi.azure.com
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: shopify-postgres-data-dev
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 30Gi
  storageClassName: azure-disk-sc-shopify-db-dev
