apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: account-service-dev
  name: account-service-dev
spec:
  replicas: '{{ .Values.replicaCount }}'
  selector:
    matchLabels:
      app: account-service-dev
  template:
    metadata:
      labels:
        app: account-service-dev
    spec:
      containers:
      - env:
        - name: DATABASE_ACCOUNT_URL
          value: ********************************************************************/account_db_dev?sslmode=disable
        - name: SERVER_PORT
          value: '8081'
        - name: SERVER_HOST
          value: 0.0.0.0
        image: '{{ .Values.image.repository }}:{{ .Values.image.tag }}'
        imagePullPolicy: '{{ .Values.image.pullPolicy }}'
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8081
          timeoutSeconds: 5
        name: account-service-dev
        ports:
        - containerPort: 8081
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8081
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 400m
            memory: 1500Mi
          requests:
            cpu: 100m
            memory: 500Mi
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: account-service-dev
  name: account-service-dev
spec:
  ports:
  - name: grpc
    port: '{{ .Values.service.port }}'
    targetPort: 8081
  selector:
    app: account-service-dev
  type: '{{ .Values.service.type }}'
