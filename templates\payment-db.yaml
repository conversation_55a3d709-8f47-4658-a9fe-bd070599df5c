apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: payment-db-dev
  name: payment-db-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: payment-db-dev
  serviceName: payment-db-dev
  template:
    metadata:
      labels:
        app: payment-db-dev
    spec:
      containers:
      - env:
        - name: POSTGRES_USER
          value: payment_service_user
        - name: POSTGRES_PASSWORD
          value: securepassword
        - name: POSTGRES_DB
          value: payment_service_db
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        image: logicontainer.azurecr.io/logiance-logistics-dev:payment_db_{{Build.BuildId}}
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -h localhost -U payment_service_user -d payment_service_db
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        name: payment-db-dev
        ports:
        - containerPort: 5432
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -h localhost -U payment_service_user -d payment_service_db
          failureThreshold: 5
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 500m
            memory: 2048Mi
          requests:
            cpu: 100m
            memory: 500Mi
        volumeMounts:
        - mountPath: /var/lib/postgresql/data
          name: payment-postgres-data
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
      volumes:
      - name: payment-postgres-data
        persistentVolumeClaim:
          claimName: payment-postgres-data-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: payment-db-dev
  name: payment-db-dev
spec:
  clusterIP: None
  ports:
  - name: tcp
    port: '{{ .Values.service.port }}'
    targetPort: 5432
  selector:
    app: payment-db-dev
  type: '{{ .Values.service.type }}'
---
allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: azure-disk-sc-payment-db-dev
parameters:
  skuName: StandardSSD_LRS
provisioner: disk.csi.azure.com
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: payment-postgres-data-dev
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 30Gi
  storageClassName: azure-disk-sc-payment-db-dev
