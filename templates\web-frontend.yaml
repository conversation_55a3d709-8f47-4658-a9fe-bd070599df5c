apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: web-frontend-dev
  name: web-frontend-dev
spec:
  replicas: '{{ .Values.replicaCount }}'
  selector:
    matchLabels:
      app: web-frontend-dev
  template:
    metadata:
      labels:
        app: web-frontend-dev
    spec:
      containers:
      - env:
        - name: NODE_ENV
          value: development
        - name: NEXT_TELEMETRY_DISABLED
          value: '1'
        - name: NEXTAUTH_SECRET
          value: J69F1Rbf9eJqOIOreESWUjJlnGF74sMM0Pr2zsE4ZrI
        - name: NEXTAUTH_URL
          value: http://localhost:3000
        - name: WATCHPACK_POLLING
          value: 'true'
        - name: CHOKIDAR_USEPOLLING
          value: 'true'
        - name: NEXT_PUBLIC_GRAPHQL_ENDPOINT
          value: graphql-service-dev:8084/graphql
        - name: RAZORPAY_KEY_ID
          value: rzp_test_PRtp0YZ64zglNX
        - name: RAZORPAY_SECRET_KEY
          value: BNEOPplaEU9FhTHFOrnDAEDp
        image: '{{ .Values.image.repository }}:{{ .Values.image.tag }}'
        imagePullPolicy: '{{ .Values.image.pullPolicy }}'
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 3000
          timeoutSeconds: 5
        name: server
        ports:
        - containerPort: 3000
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 3000
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 1000m
            memory: 4048Mi
          requests:
            cpu: 200m
            memory: 1000Mi
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-tcp-idle-timeout: '30'
  labels:
    app: web-frontend-dev
  name: web-frontend-dev
spec:
  ports:
  - name: http
    port: '{{ .Values.service.port }}'
    targetPort: 3000
  selector:
    app: web-frontend-dev
  type: '{{ .Values.service.type }}'
