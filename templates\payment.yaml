apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: payment-service-dev
  name: payment-service-dev
spec:
  replicas: '{{ .Values.replicaCount }}'
  selector:
    matchLabels:
      app: payment-service-dev
  template:
    metadata:
      labels:
        app: payment-service-dev
    spec:
      containers:
      - env:
        - name: ENV
          value: production
        - name: HTTP_PORT
          value: '8080'
        - name: GRPC_PORT
          value: '50051'
        - name: DB_HOST
          value: payment-db-dev
        - name: DB_PORT
          value: '5432'
        - name: DB_USER
          value: payment_service_user
        - name: DB_PASSWORD
          value: securepassword
        - name: DB_NAME
          value: payment_service_db
        - name: DB_SSL_MODE
          value: require
        - name: RAZORPAY_KEY
          value: rzp_test_rip1Sycexmv3Ld
        - name: RAZORPAY_SECRET
          value: RKZcT3qWwjXTZ1leYLeSqccF
        image: '{{ .Values.image.repository }}:{{ .Values.image.tag }}'
        imagePullPolicy: '{{ .Values.image.pullPolicy }}'
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 50051
          timeoutSeconds: 5
        name: payment-service-dev
        ports:
        - containerPort: 50051
          name: grpc
        - containerPort: 8080
          name: http
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 50051
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 400m
            memory: 1500Mi
          requests:
            cpu: 100m
            memory: 500Mi
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: payment-service-dev
  name: payment-service-dev
spec:
  ports:
  - name: grpc
    port: '{{ .Values.service.port }}'
    targetPort: 50051
  - name: http
    port: 8080
    targetPort: 8080
  selector:
    app: payment-service-dev
  type: '{{ .Values.service.type }}'
