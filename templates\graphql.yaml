apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: graphql-service-dev
  name: graphql-service-dev
spec:
  replicas: '{{ .Values.replicaCount }}'
  selector:
    matchLabels:
      app: graphql-service-dev
  template:
    metadata:
      labels:
        app: graphql-service-dev
    spec:
      containers:
      - env:
        - name: ACCOUNT_SERVICE_URL
          value: account-service-dev:8081
        - name: PAYMENT_SERVICE_URL
          value: payment-service-dev:8080
        - name: SHOPIFY_SERVICE_URL
          value: shopify-service-dev:8080
        - name: BLUEDART_SERVICE_URL
          value: bluedart-service-dev:8081
        - name: XPRESSBEES_SERVICE_URL
          value: xpressbees-service-dev:9000
        - name: DELHIVERY_SERVICE_URL
          value: delhivery-service-dev:50051
        - name: ALLOWED_ORIGINS
          value: http://web-frontend-dev:3000
        - name: NDR_SERVICE_URL
          value: ndr-service-dev:8080
        - name: PORT
          value: '8084'
        - name: KAFKA_BROKERS
          value: kafka-controller-0.kafka-controller-headless.infra.svc.cluster.local:9092
        - name: KAFKA_TOPIC
          value: order-status-updates
        - name: WORKER_POOL_SIZE
          value: '5'
        - name: REDIS_ADDR
          value: redis-master.infra.svc.cluster.local:6379
        - name: REDIS_PASSWORD
          value: Redis@logi
        - name: REDIS_DB
          value: '0'
        - name: REDIS_POOL_SIZE
          value: '100'
        - name: REDIS_MIN_IDLE_CONNS
          value: '10'
        - name: REDIS_TTL_DAYS
          value: '30'
        image: '{{ .Values.image.repository }}:{{ .Values.image.tag }}'
        imagePullPolicy: '{{ .Values.image.pullPolicy }}'
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8084
          timeoutSeconds: 5
        name: graphql-service-dev
        ports:
        - containerPort: 8084
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8084
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 1000m
            memory: 3048Mi
          requests:
            cpu: 200m
            memory: 1000Mi
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: graphql-service-dev
  name: graphql-service-dev
spec:
  ports:
  - name: grpc
    port: '{{ .Values.service.port }}'
    targetPort: 8084
  selector:
    app: graphql-service-dev
  type: '{{ .Values.service.type }}'
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: graphql-service-dev
  name: graphql-service-internet
spec:
  ports:
  - name: http
    port: '{{ .Values.service.port }}'
    targetPort: 8084
  selector:
    app: graphql-service-dev
  type: '{{ .Values.service.type }}'
