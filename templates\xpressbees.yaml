apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: xpressbees-service-dev
  name: xpressbees-service-dev
spec:
  replicas: '{{ .Values.replicaCount }}'
  selector:
    matchLabels:
      app: xpressbees-service-dev
  template:
    metadata:
      labels:
        app: xpressbees-service-dev
    spec:
      containers:
      - env:
        - name: ENVIRONMENT
          value: production
        - name: LOG_LEVEL
          value: info
        - name: GRPC_PORT
          value: '9000'
        - name: XB_API_URL
          value: https://shipment.xpressbees.com/
        - name: HTTP_TIMEOUT_SECONDS
          value: '60'
        - name: DB_HOST
          value: xpressbees-db-dev
        - name: DB_PORT
          value: '5432'
        - name: DB_USER
          value: xpressbees_user_dev
        - name: DB_PASSWORD
          value: xpressbees_password_dev
        - name: DB_NAME
          value: xpressbees_db
        - name: DB_SSLMODE
          value: disable
        - name: AZURE_CONNECTION_STRING
          value: DefaultEndpointsProtocol=https;AccountName=labelstore;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
        - name: AZURE_CONTAINER_NAME
          value: xpressbees
        - name: XB_EMAIL
          value: <EMAIL>
        - name: XB_PASSWORD
          value: xxxx
        image: '{{ .Values.image.repository }}:{{ .Values.image.tag }}'
        imagePullPolicy: '{{ .Values.image.pullPolicy }}'
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 9000
          timeoutSeconds: 5
        name: xpressbees-service-dev
        ports:
        - containerPort: 9000
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 9000
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 400m
            memory: 1500Mi
          requests:
            cpu: 100m
            memory: 500Mi
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: xpressbees-service-dev
  name: xpressbees-service-dev
spec:
  ports:
  - name: grpc
    port: '{{ .Values.service.port }}'
    targetPort: 9000
  selector:
    app: xpressbees-service-dev
  type: '{{ .Values.service.type }}'
