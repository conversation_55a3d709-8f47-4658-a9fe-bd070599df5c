apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: shopify-service-dev
  name: shopify-service-dev
spec:
  replicas: '{{ .Values.replicaCount }}'
  selector:
    matchLabels:
      app: shopify-service-dev
  template:
    metadata:
      labels:
        app: shopify-service-dev
    spec:
      containers:
      - env:
        - name: DATABASE_SHOPIFY_URL
          value: ********************************************************************/shopify_db_dev?sslmode=disable
        - name: KAFKA_BROKERS
          value: kafka-controller-0.kafka-controller-headless.infra.svc.cluster.local:9092
        - name: KAFKA_TOPIC
          value: order-status-updates
        - name: KAFKA_GROUP_ID
          value: shopify-service-consumer-group
        - name: KAFKA_MIN_BYTES
          value: '10000'
        - name: KAFKA_MAX_BYTES
          value: '10000000'
        - name: SHOPIFY_API_KEY
          value: 67f10611ac39283d047c7cc4c8e04954
        - name: SHOPIFY_API_SECRET
          value: 63e9e494ff13bddd03cb4d742baa10f0
        - name: SHOPIFY_REDIRECT_URL
          value: https://logiancelogistics.com/storeorders
        image: '{{ .Values.image.repository }}:{{ .Values.image.tag }}'
        imagePullPolicy: '{{ .Values.image.pullPolicy }}'
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        name: shopify-service-dev
        ports:
        - containerPort: 8080
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 10
          periodSeconds: 10
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 400m
            memory: 1500Mi
          requests:
            cpu: 100m
            memory: 500Mi
      tolerations:
      - effect: NoExecute
        key: app
        operator: Equal
        value: logiance-dev
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: shopify-service-dev
  name: shopify-service-dev
spec:
  ports:
  - name: grpc
    port: '{{ .Values.service.port }}'
    targetPort: 8080
  selector:
    app: shopify-service-dev
  type: '{{ .Values.service.type }}'
